import SwiftUI

// MARK: - SoundModeSettingsView Sections
extension SoundModeSettingsView {
    /// 预览播放区
    var previewSection: some View {
        VStack(alignment: .leading, spacing: AppTheme.smallPadding) {
            // 显示当前音效名称（只读提示）
            HStack {
                VStack(alignment: .leading, spacing: AppTheme.tinyPadding) {
                    Text(config.name)
                        .font(.appBody)
                        .foregroundColor(.textPrimary)
                }
                Spacer()
            }
            .standardRowStyle()

            // 播放按钮行
            Button(action: togglePreview) {
                HStack(alignment: .center) {
                    Image(systemName: isPlaying ? "stop.fill" : "play.fill")
                        .foregroundColor(isPlaying ? AppTheme.warningColor : AppTheme.primaryColor)
                        .font(.system(size: AppTheme.smallIconSize))
                        .frame(width: AppTheme.iconSize, height: AppTheme.iconSize)
                        .scaleEffect(isPlaying ? 1.1 : 1.0)
                        .animation(.easeInOut(duration: 0.2), value: isPlaying)

                    Text(isPlaying ? "停止预览" : "预览播放")
                        .font(.appBody)
                        .foregroundColor(isPlaying ? AppTheme.warningColor : Color.textPrimary)
                        .animation(.easeInOut(duration: 0.2), value: isPlaying)

                    Spacer()
                }
                .previewRowStyle(isActive: isPlaying)
                .contentShape(Rectangle())
            }
            .buttonStyle(PlainButtonStyle())
        }
    }


}


