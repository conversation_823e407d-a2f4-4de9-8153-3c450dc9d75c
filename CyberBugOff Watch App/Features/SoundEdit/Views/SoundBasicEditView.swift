import SwiftUI

/// 音效基础编辑视图 - 用于编辑音效的基础属性（名称等）
/// 与 SoundEditView 不同，这个视图不会修改原音频，只编辑音效的基础属性
struct SoundBasicEditView: View {
    @ObservedObject var model: BugOffModel
    @State private var soundName: String
    @State private var baseSound: String
    @Binding var isPresented: Bool
    
    // MARK: - State Properties
    @State private var editedName: String = ""
    @State private var originalName: String = ""
    @State private var showErrorToast: Bool = false
    @State private var errorMessage: String = ""
    
    // MARK: - Initialization
    init(model: BugOffModel, soundName: String, isPresented: Binding<Bool>) {
        self.model = model
        self._soundName = State(initialValue: soundName)
        self._baseSound = State(initialValue: soundName)
        self._isPresented = isPresented
        self._editedName = State(initialValue: soundName)
        self._originalName = State(initialValue: soundName)
    }
    
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: AppTheme.mediumPadding) {
                // 名称编辑区域
                nameEditSection

                // 操作按钮
                actionsSection
            }
            .padding(.top, AppTheme.smallPadding)
        }
        .navigationTitle("编辑音效")
        .navigationBarTitleDisplayMode(.inline)
        .toast(message: errorMessage, isVisible: $showErrorToast)
        .onAppear {
            // 停止其他视图正在播放的音效
            model.stopSound()
        }
    }
    
    // MARK: - View Sections
    
    /// 名称编辑区域
    private var nameEditSection: some View {
        renameSection
    }
    
    
    /// 操作按钮区域
    private var actionsSection: some View {
        VStack(spacing: AppTheme.smallPadding) {
            // 保存按钮
            Button(action: {
                saveChanges()
                isPresented = false
            }) {
                HStack {
                    Image(systemName: "checkmark")
                        .foregroundColor(AppTheme.primaryColor)
                        .font(.system(size: AppTheme.smallIconSize))

                    Text("保存")
                        .font(.appBody)
                        .foregroundColor(.textPrimary)

                    Spacer()
                }
                .standardRowStyle()
            }
            .buttonStyle(PlainButtonStyle())
            .disabled(editedName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)

            // 重置按钮
            Button(action: resetName) {
                HStack {
                    Image(systemName: "arrow.counterclockwise")
                        .foregroundColor(AppTheme.warningColor)
                        .font(.system(size: AppTheme.smallIconSize))

                    Text("重置名称")
                        .font(.appBody)
                        .foregroundColor(.textPrimary)

                    Spacer()
                }
                .actionRowStyle(.warning)
            }
            .buttonStyle(PlainButtonStyle())


        }
    }
    
    private var renameSection: some View {
        StandardTextField(
            text: $soundName,
            onTextChange: { newName in
                // 在此处可以添加一些实时验证逻辑，如果需要
            }
        )
    }
    
    // MARK: - Actions
    
    /// 保存更改
    private func saveChanges() {
        let trimmedName = editedName.trimmingCharacters(in: .whitespacesAndNewlines)
        
        // 检查名称是否为空
        guard !trimmedName.isEmpty else {
            errorMessage = "音效名称不能为空"
            showErrorToast = true
            return
        }
        
        // 如果名称没有变化，直接关闭
        if trimmedName == originalName {
            isPresented = false
            return
        }
        
        // 检查名称是否已存在
        if model.defaultSounds.contains(trimmedName) && trimmedName != originalName {
            errorMessage = "名称已存在，请使用其他名称"
            showErrorToast = true
            return
        }
        
        // 执行重命名
        if let index = model.defaultSounds.firstIndex(of: originalName) {
            model.defaultSounds[index] = trimmedName
            
            // 更新所有相关的配置
            updateRelatedConfigs(from: originalName, to: trimmedName)
            
            print("音效已重命名：\(originalName) → \(trimmedName)")
            isPresented = false
        } else {
            errorMessage = "找不到原音效，重命名失败"
            showErrorToast = true
        }
    }
    
    /// 重置名称
    private func resetName() {
        editedName = originalName
    }
    
    /// 更新相关配置
    private func updateRelatedConfigs(from oldName: String, to newName: String) {
        // 更新音效配置中的引用
        var updatedConfigs: [String: SoundConfig] = [:]
        
        for (key, config) in model.soundConfigs {
            var updatedConfig = config
            
            // 如果配置的基础音效名称是旧名称，创建新的配置
            if updatedConfig.baseSoundName == oldName {
                var newConfig = SoundConfig(
                    name: updatedConfig.name,
                    baseSoundName: newName
                )
                // 复制其他属性
                newConfig.playbackRate = updatedConfig.playbackRate
                newConfig.volume = updatedConfig.volume
                newConfig.startTime = updatedConfig.startTime
                newConfig.endTime = updatedConfig.endTime
                updatedConfig = newConfig
            }
            
            // 如果配置的名称是旧名称，更新为新名称
            if updatedConfig.name == oldName {
                updatedConfig.name = newName
                // 使用新名称作为键
                updatedConfigs[newName] = updatedConfig
            } else {
                updatedConfigs[key] = updatedConfig
            }
        }
        
        model.soundConfigs = updatedConfigs

        // TODO: 更新合成音效中的引用
        // TODO: 更新图片设置中的音效引用
        print("音效名称已更新：\(oldName) → \(newName)")
    }
}

// MARK: - Preview
#Preview {
    SoundBasicEditView(
        model: BugOffModel(),
        soundName: "示例音效",
        isPresented: .constant(true)
    )
}
